<template>
  <div class="flex flex-col h-full">
    <div class="flex flex-col gap-4 flex-1 min-h-0">
      <!-- Phần bộ lọc -->
      <div class="filters-container bg-white p-4 rounded shadow-sm flex flex-wrap gap-4 items-end">
        <div class="filter-group">
          <label for="adminUnitFilter" class="block text-sm font-medium text-gray-700 mb-1">Khu vực hành chính:</label>
          <select id="adminUnitFilter" v-model="selectedAdminUnit"
            class="p-2 border border-gray-300 rounded min-w-[180px]">
            <option value="">Tất cả</option>
            <option v-for="unit in uniqueAdminUnits" :key="unit" :value="unit">
              {{ unit }}
            </option>
          </select>
        </div>
        <div class="filter-group">
          <label for="assetTypeFilter" class="block text-sm font-medium text-gray-700 mb-1">Loại tài sản:</label>
          <select id="assetTypeFilter" v-model="selectedAssetType"
            class="p-2 border border-gray-300 rounded min-w-[180px]">
            <option value="">Tất cả</option>
            <option v-for="type in uniqueAssetTypes" :key="type" :value="type">
              {{ type }}
            </option>
          </select>
        </div>
        <!-- <div class="filter-group">
          <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Tình trạng:</label>
          <select id="statusFilter" v-model="selectedStatus" class="p-2 border border-gray-300 rounded min-w-[180px]">
            <option v-for="status in statusOptions" :key="status.value" :value="status.value">
              {{ status.text }}
            </option>
          </select>
        </div> -->
        <div class="filter-group">
          <label for="chartDisplayModeFilter" class="block text-sm font-medium text-gray-700 mb-1">Hiển thị biểu đồ
            theo:</label>
          <select id="chartDisplayModeFilter" v-model="chartDisplayBy"
            class="p-2 border border-gray-300 rounded min-w-[180px]">
            <option value="value">Nguyên giá tài sản</option>
            <option value="count">Số lượng tài sản</option>
          </select>
        </div>
        <div class="filter-group">
          <button @click="fetchStats"
            class="px-4 py-2 bg-[#0077b6] text-white rounded hover:bg-[#004c75] disabled:opacity-50"
            :disabled="loading">
            {{ loading ? 'Đang tải...' : 'Xem thống kê' }} </button>
        </div>
      </div>

      <!-- Phần biểu đồ -->
      <div class="bg-white p-4 rounded shadow-md flex-1 min-h-0 flex flex-col">
        <div v-if="!hasData"
          class="h-full flex items-center justify-center text-gray-500 border-2 border-dashed border-gray-300 rounded text-base font-semibold">
          Biểu đồ thống kê sẽ được hiển thị tại đây
        </div>
        <div v-else class="h-full flex flex-col">
          <h3 class="mb-4 text-base font-semibold">{{ chartTitle }}</h3>
          <div class="flex-1 min-h-0">
            <v-chart class="w-full h-full" :option="chartOption" autoresize />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import axios from 'axios'
// import DataTable from '@/components/DataTable.vue' // Không sử dụng
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// Đăng ký các component ECharts cần thiết
use([
  CanvasRenderer,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// Props từ component cha
const props = defineProps<{
  reportTypeName: string
}>()

// State cho các bộ lọc
const selectedAdminUnit = ref('');
const selectedAssetType = ref('');
const selectedStatus = ref(''); // '' cho tất cả, 'active', 'inactive'
const chartDisplayBy = ref<'value' | 'count'>('value'); // Mặc định hiển thị theo nguyên giá

const statusOptions = ref([
  { value: '', text: 'Tất cả tình trạng' },
  { value: 'active', text: 'Đang hoạt động' },
  { value: 'inactive', text: 'Không hoạt động' },
]);

// State để lưu dữ liệu từ API
const loading = ref(false)
const error = ref(null)
const reportData = ref([])
// const stats = ref(null) // Sẽ không dùng stats trực tiếp cho biểu đồ nữa

// Computed properties cho các tùy chọn bộ lọc
const uniqueAdminUnits = computed(() => {
  // Sử dụng danh sách cố định thay vì lấy từ props.reportData
  return [
    'Phường Tân Định',
    'Thị trấn Tân Túc',
    'Xã Phạm Văn Hai',
    'Xã Bình Lợi',
    'Xã Tân Nhựt',
    'Xã Tân Kiên',
    'Xã Phong Phú',
    'Xã An Phú Tây',
    'Xã Hưng Long',
    'Xã Đa Phước',
    'Xã Tân Quý Tây',
    'Xã Quy Đức',
    // Thêm các đơn vị khác nếu cần
  ].sort(); // Sắp xếp theo alphabet cho dễ nhìn
});

const uniqueAssetTypes = computed(() => {
  return [
    'Cống đập',
    'Trạm bơm',
    'Kênh mương'
  ]
});

// Computed property cho dữ liệu đã lọc
const hasData = computed(() => reportData.value && reportData.value.length > 0)

// Tính toán dữ liệu cho biểu đồ từ reportData (đã được lọc)
const chartDataProcessed = computed(() => {
  if (!reportData.value || reportData.value.length === 0) return [];
  interface ReportItem {
    unit?: string;
    originalPrice?: string | number;
    [key: string]: any; // Cho phép các thuộc tính khác
  }
  interface GroupedData {
    [key: string]: { total_value: number; count: number };
  }
  const groupedByAdminUnit = reportData.value.reduce((acc: GroupedData, item: ReportItem) => {
    // Sử dụng item.unit vì đó là trường được dùng cho "Đơn vị quản lý" trong columnDefs
    // và có vẻ là ключом для группировки в uniqueAdminUnits
    const adminUnit = item.unit || 'Không xác định';
    if (!acc[adminUnit]) {
      acc[adminUnit] = { total_value: 0, count: 0 };
    }
    // item.originalPrice đã được parseFloat trong fetchStats và là một số
    acc[adminUnit].total_value += (Number(item.originalPrice) || 0); // Sử dụng Number() để đảm bảo là số, hoặc đơn giản là item.originalPrice
    acc[adminUnit].count += 1;
    return acc;
  }, {});

  return Object.entries(groupedByAdminUnit).map(([unitName, data]: [string, any]) => ({
    name: unitName,
    value: chartDisplayBy.value === 'value' ? data.total_value : data.count,
  }));
});

const chartTitle = computed(() => {
  const displayModeText = chartDisplayBy.value === 'value' ? 'Theo Nguyên giá' : 'Theo Số lượng';
  return `${props.reportTypeName} (${displayModeText})`;
});
// Hàm gọi API
const fetchStats = async () => {
  loading.value = true
  error.value = null
  try {
    const params = {
      xa_phuong: selectedAdminUnit.value || undefined, // Nếu value là "" (Tất cả), gửi undefined
      loai_tai_san: selectedAssetType.value || undefined, // Nếu value là "" (Tất cả), gửi undefined
      tinh_trang: selectedStatus.value || undefined
    }

    const response = await axios.get('/api/assets/stats', { params })

    if (response.data.success) {
      // Giả sử response.data.data là một object chứa { data: [], stats: {} }
      // Bảng cần mảng dữ liệu, biểu đồ cần object stats.
      // Chúng ta sẽ sử dụng response.data.data.data cho cả bảng và biểu đồ
      if (response.data.data && Array.isArray(response.data.data.data)) {
        // Đảm bảo reportData.value là một mảng cho DataTable
        // Map dữ liệu để đảm bảo các trường cần thiết có định dạng đúng (ví dụ: originalPrice là số)
        reportData.value = response.data.data.data.map((item: any) => ({
          ...item, // Giữ lại các trường gốc từ API, bao gồm original_price
          originalPrice: parseFloat(item.original_price) || 0, // Parse từ item.original_price (snake_case)
          // Giữ lại các trường khác mà bảng hoặc logic khác có thể cần
          // Ví dụ: item.unit, item.name, item.code, etc.
        }));
        // stats.value không còn cần thiết cho việc vẽ biểu đồ này nữa nếu tính từ reportData
        // stats.value = response.data.data.stats || null; 
      } else {
        reportData.value = []
        // stats.value = null;
      }
    }
  } catch (e: any) {
    error.value = e.message || 'Có lỗi xảy ra khi tải dữ liệu'
    console.error('Error fetching stats:', e)
  } finally {
    loading.value = false
  }
}

// Watch các bộ lọc để tự động gọi API khi có thay đổi
// watch([selectedAdminUnit, selectedAssetType, selectedStatus], () => {
//   fetchStats()
// })

// Gọi API lần đầu khi component được mount
// onMounted(() => {
//   fetchStats()
// })

// Cập nhật biểu đồ để sử dụng dữ liệu đã lọc
const chartOption = computed(() => ({
  // Thêm một mảng màu sắc tùy chỉnh tại đây
  // ECharts sẽ sử dụng các màu này theo thứ tự cho các lát cắt của biểu đồ tròn
  color: [
    '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE',
    '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC',
    // Thêm các màu khác nếu bạn có nhiều đơn vị hơn
    '#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae',
    '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570',
    '#c4ccd3'
  ],
  // title.text sẽ được hiển thị bởi <h3>{{ chartTitle }}</h3>
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      const unitSuffix = chartDisplayBy.value === 'value' ? '' : ' tài sản';
      const valueFormatted = chartDisplayBy.value === 'value'
        ? formatCurrency(params.value)
        : params.value.toLocaleString('vi-VN');
      return `${params.seriesName}<br/>${params.name}: ${valueFormatted}${unitSuffix} (${params.percent}%)`;
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'center'
  },
  series: [
    {
      name: chartDisplayBy.value === 'value' ? 'Nguyên giá tài sản' : 'Số lượng tài sản',
      type: 'pie',
      radius: ['40%', '70%'], // Làm biểu đồ hình vành khuyên cho đẹp hơn
      data: chartDataProcessed.value, // Sử dụng dữ liệu đã xử lý từ filteredReportData
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
      // label: { // Cân nhắc hiển thị label nếu muốn
      //   show: true,
      //   formatter: '{b}: {d}%'
      // }
    }
  ]
}))

// Helper functions
function formatCurrency(value: number) {
  if (value === undefined || value === null) return '';
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND', minimumFractionDigits: 0 }).format(value);
}
</script>