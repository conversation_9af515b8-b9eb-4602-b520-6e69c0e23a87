<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

// Data state
const activityLogs = ref<any[]>([])
const loading = ref(false)
const error = ref('')

// Pagination state
const currentPage = ref(1)
const perPage = ref(15)
const totalPages = ref(1)
const totalRecords = ref(0)
const fromRecord = ref(0)
const toRecord = ref(0)

// Filter state
const filters = ref({
  dateFrom: '',
  dateTo: '',
  userId: '',
  action: '',
  search: ''
})

// Options for filters
const userOptions = ref<any[]>([])
const actionOptions = ref<any[]>([])

// Fetch data function
const fetchData = async (page = 1) => {
  loading.value = true
  error.value = ''

  try {
    const params: any = {
      page,
      per_page: perPage.value
    }

    // Add filters if they have values
    if (filters.value.dateFrom) params.date_from = filters.value.dateFrom
    if (filters.value.dateTo) params.date_to = filters.value.dateTo
    if (filters.value.userId) params.user_id = filters.value.userId
    if (filters.value.action) params.action = filters.value.action
    if (filters.value.search) params.search = filters.value.search

    const response = await axios.get('/api/activity-logs', { params })

    if (response.data.success) {
      activityLogs.value = response.data.data || []

      // Update pagination info
      const meta = response.data.meta
      if (meta) {
        currentPage.value = meta.current_page
        totalPages.value = meta.last_page
        totalRecords.value = meta.total
        fromRecord.value = meta.from || 0
        toRecord.value = meta.to || 0
      }
    } else {
      error.value = 'Không thể tải dữ liệu lịch sử hoạt động'
    }
  } catch (e: any) {
    console.error('Error fetching activity logs:', e)
    error.value = 'Lỗi khi tải dữ liệu lịch sử hoạt động'
  } finally {
    loading.value = false
  }
}

// Fetch filter options
const fetchFilterOptions = async () => {
  try {
    // Fetch users
    const usersResponse = await axios.get('/api/activity-logs/filters/users')
    if (usersResponse.data.success) {
      userOptions.value = usersResponse.data.data || []
    }

    // Fetch actions
    const actionsResponse = await axios.get('/api/activity-logs/filters/actions')
    if (actionsResponse.data.success) {
      actionOptions.value = actionsResponse.data.data || []
    }
  } catch (e: any) {
    console.error('Error fetching filter options:', e)
  }
}

// Apply filters
const applyFilters = () => {
  currentPage.value = 1
  fetchData(1)
}

// Reset filters
const resetFilters = () => {
  filters.value = {
    dateFrom: '',
    dateTo: '',
    userId: '',
    action: '',
    search: ''
  }
  currentPage.value = 1
  fetchData(1)
}

// Refresh data
const refreshData = () => {
  fetchData(currentPage.value)
}

// Export to Excel (placeholder)
const exportToExcel = () => {
  // TODO: Implement export functionality
  console.log('Export to Excel functionality to be implemented')
}

// Pagination handlers
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    fetchData(page)
  }
}

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1)
  }
}

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    goToPage(currentPage.value + 1)
  }
}

// Computed pagination info
const paginationInfo = computed(() => {
  if (totalRecords.value === 0) {
    return 'Không có dữ liệu'
  }
  return `Hiển thị ${fromRecord.value} đến ${toRecord.value} của ${totalRecords.value} bản ghi`
})

// Initialize component
onMounted(async () => {
  await Promise.all([
    fetchData(),
    fetchFilterOptions()
  ])
})

const getActionClass = (action: string) => {
  switch (action) {
    case 'Đăng nhập':
    case 'Tạo mới':
    case 'Cập nhật':
    case 'Phê duyệt':
      return 'bg-blue-100 text-blue-800'
    case 'Từ chối':
    case 'Xóa':
      return 'bg-red-100 text-red-800'
    case 'Xuất báo cáo':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Lịch Sử Hoạt Động
      </h2>
      <div class="flex items-center gap-2">
        <Button variant="outline" @click="exportToExcel">
          <Icon name="Download" class="h-4 w-4 mr-2" />
          Xuất Excel
        </Button>
        <Button @click="refreshData" :disabled="loading">
          <Icon name="RefreshCw" :class="loading ? 'h-4 w-4 mr-2 animate-spin' : 'h-4 w-4 mr-2'" />
          Làm mới
        </Button>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4 p-4 bg-muted/50 rounded-lg">
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Từ ngày</label>
        <Input type="date" class="w-40" v-model="filters.dateFrom" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Đến ngày</label>
        <Input type="date" class="w-40" v-model="filters.dateTo" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Người dùng</label>
        <select class="border rounded px-2 py-1.5 w-48" v-model="filters.userId">
          <option value="">Tất cả người dùng</option>
          <option v-for="user in userOptions" :key="user.id" :value="user.id">
            {{ user.name }}
          </option>
        </select>
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Loại hoạt động</label>
        <select class="border rounded px-2 py-1.5 w-48" v-model="filters.action">
          <option value="">Tất cả hoạt động</option>
          <option v-for="action in actionOptions" :key="action.value" :value="action.value">
            {{ action.label }}
          </option>
        </select>
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Tìm kiếm</label>
        <Input
          type="text"
          class="w-48"
          placeholder="Tìm kiếm mô tả..."
          v-model="filters.search"
          @keyup.enter="applyFilters"
        />
      </div>
      <div class="self-end flex gap-2">
        <Button @click="applyFilters" :disabled="loading">
          <Icon name="Filter" class="h-4 w-4 mr-2" />
          Lọc
        </Button>
        <Button variant="outline" @click="resetFilters" :disabled="loading">
          <Icon name="RotateCcw" class="h-4 w-4 mr-2" />
          Đặt lại
        </Button>
      </div>
    </div>
    <!-- Error message -->
    <div v-if="error" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-red-600">{{ error }}</p>
    </div>

    <div class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THỜI GIAN
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              NGƯỜI DÙNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              HOẠT ĐỘNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              MÔ TẢ
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              ĐỊA CHỈ IP
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THIẾT BỊ
            </th>
          </tr>
        </thead>
        <tbody>
          <!-- Loading state -->
          <tr v-if="loading">
            <td colspan="6" class="p-8 text-center text-muted-foreground">
              <Icon name="Loader2" class="h-6 w-6 animate-spin mx-auto mb-2" />
              <p>Đang tải dữ liệu...</p>
            </td>
          </tr>

          <!-- Empty state -->
          <tr v-else-if="activityLogs.length === 0">
            <td colspan="6" class="p-8 text-center text-muted-foreground">
              <Icon name="FileX" class="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Không có dữ liệu lịch sử hoạt động</p>
            </td>
          </tr>

          <!-- Data rows -->
          <tr v-else v-for="(log, index) in activityLogs" :key="log.id || index" class="border-b last:border-b-0 hover:bg-muted/30">
            <td class="p-3 text-sm">
              {{ log.time }}
            </td>
            <td class="p-3 font-medium">
              {{ log.user }}
            </td>
            <td class="p-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getActionClass(log.action)"
              >
                {{ log.action }}
              </span>
            </td>
            <td class="p-3 text-sm">
              {{ log.description }}
            </td>
            <td class="p-3 text-sm">
              {{ log.ip }}
            </td>
            <td class="p-3 text-sm">
              {{ log.device }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- Pagination -->
    <div class="flex justify-between items-center mt-4">
      <span class="text-sm text-muted-foreground">{{ paginationInfo }}</span>

      <div v-if="totalPages > 1" class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          @click="goToPreviousPage"
          :disabled="currentPage <= 1 || loading"
        >
          <Icon name="ChevronLeft" class="h-4 w-4" />
        </Button>

        <div class="flex items-center gap-1">
          <Button
            v-for="page in Math.min(totalPages, 5)"
            :key="page"
            variant="outline"
            size="sm"
            :class="{ 'bg-primary text-primary-foreground': page === currentPage }"
            @click="goToPage(page)"
            :disabled="loading"
          >
            {{ page }}
          </Button>

          <span v-if="totalPages > 5" class="px-2 text-muted-foreground">...</span>

          <Button
            v-if="totalPages > 5 && currentPage < totalPages - 2"
            variant="outline"
            size="sm"
            @click="goToPage(totalPages)"
            :disabled="loading"
          >
            {{ totalPages }}
          </Button>
        </div>

        <Button
          variant="outline"
          size="sm"
          @click="goToNextPage"
          :disabled="currentPage >= totalPages || loading"
        >
          <Icon name="ChevronRight" class="h-4 w-4" />
        </Button>
      </div>
    </div>
  </div>
</template>
