# @format

<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black/75 transition-opacity"></div>

    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4 text-center">
      <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full max-w-lg">
        <!-- Header -->
        <div class="bg-white px-4 pt-5 pb-4">
          <div class="mb-4">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Thông tin đơn vị báo cáo
            </h3>
            <p class="mt-1 text-sm text-gray-500">
              Vui lòng điền đầy đủ thông tin trước khi tiếp tục
            </p>
          </div>

          <!-- Form -->
          <form @submit.prevent="handleSubmit" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Tên đối tượng <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  v-model="formData.name"
                  required
                  class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-[#0077b6] focus:outline-none focus:ring-1 focus:ring-[#0077b6]"
                  placeholder="Nhập tên đối tượng"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Mã đơn vị <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  v-model="formData.code"
                  required
                  class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-[#0077b6] focus:outline-none focus:ring-1 focus:ring-[#0077b6]"
                  placeholder="Nhập mã đơn vị"
                />
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Địa chỉ <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  v-model="formData.address"
                  required
                  class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-[#0077b6] focus:outline-none focus:ring-1 focus:ring-[#0077b6]"
                  placeholder="Nhập địa chỉ"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">
                  Số điện thoại liên hệ <span class="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  v-model="formData.phone"
                  required
                  pattern="[0-9]{10,11}"
                  class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-[#0077b6] focus:outline-none focus:ring-1 focus:ring-[#0077b6]"
                  placeholder="Nhập số điện thoại"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">
                Loại hình đơn vị <span class="text-red-500">*</span>
              </label>
              <select
                v-model="formData.type"
                required
                class="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-[#0077b6] focus:outline-none focus:ring-1 focus:ring-[#0077b6]"
              >
                <option value="" disabled>Chọn loại hình đơn vị</option>
                <option value="state">Cơ quan nhà nước</option>
                <option value="public">Đơn vị sự nghiệp công lập</option>
                <option value="enterprise">Doanh nghiệp nhà nước</option>
              </select>
            </div>
          </form>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-4 py-3 flex flex-row-reverse gap-2">
          <button
            type="button"
            @click="handleSubmit"
            class="inline-flex justify-center rounded-md border border-transparent bg-[#0077b6] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#004c75] focus:outline-none focus:ring-2 focus:ring-[#0077b6] focus:ring-offset-2"
          >
            Xác nhận
          </button>
          <button
            type="button"
            @click="closeModal"
            class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#0077b6] focus:ring-offset-2"
          >
            Hủy
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue'

const props = defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
  'submit': [data: typeof formData.value]
}>()

const formData = ref({
  name: '',
  code: '',
  address: '',
  phone: '',
  type: ''
})

function closeModal() {
  emit('update:isOpen', false)
}

function handleSubmit() {
  if (
    formData.value.name &&
    formData.value.code &&
    formData.value.address &&
    formData.value.phone &&
    formData.value.type
  ) {
    emit('submit', { ...formData.value })
    closeModal()
  }
}
</script>
