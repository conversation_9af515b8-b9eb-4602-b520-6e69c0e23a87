<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Normalizer;
use Illuminate\Support\Str;

class Normalizedata extends Command
{
    protected $signature = 'normalize:data';
    protected $description = 'Chuẩn hoá dữ liệu cột (Unicode, khoảng trắng, chữ thường)';

    public function handle()
    {
        $field = 'tenloai_hs';
        $rows = DB::table('xaydung.hoso_xd')  // ← sửa tên bảng thật
            ->select('id', $field)
            ->whereNotNull($field)
            ->get();

        foreach ($rows as $row) {
            $old = $row->$field;
            $normalized = mb_convert_encoding($old, 'UTF-8', mb_detect_encoding($old, mb_detect_order(), true));
            $normalized = Normalizer::normalize($old, Normalizer::FORM_C);
            $normalized = preg_replace('/\s+/u', ' ', trim($normalized));
            // Chuy<PERSON><PERSON> về chữ thường, r<PERSON><PERSON> viết hoa chữ cái đầ
            //$normalized = mb_convert_case(mb_strtolower($normalized, 'UTF-8'), MB_CASE_TITLE, 'UTF-8');

            if ($normalized !== $old) {
                DB::table('xaydung.hoso_xd')
                    ->where('id', $row->id)
                    ->update([$field => $normalized]);

                $this->info("Cập nhật ID {$row->id}: '{$old}' → '{$normalized}'");
            }
        }

        $this->info('✅ Đã chuẩn hoá xong cột '.$field.'.');
    }
}
