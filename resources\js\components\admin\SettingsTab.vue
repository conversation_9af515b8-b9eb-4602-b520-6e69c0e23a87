<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

const settings = ref({
  general: {
    systemName: 'Hệ Thống Quản Lý <PERSON> Sở Dữ Liệu Tài Sản Kết Cấu Hạ Tầng Thủy Lợi',
    managingUnit: 'Bộ Nông nghiệp và Phát triển Nông thôn',
    contactEmail: '<EMAIL>',
    supportPhone: '1900 1234',
  },
  map: {
    defaultBasemap: 'Google Maps',
    defaultLon: '105.8342',
    defaultLat: '21.0278',
    defaultZoom: 6,
    allow3D: true,
  },
  security: {
    sessionTimeout: 30,
    minPasswordLength: 8,
    maxLoginAttempts: 5,
    require2FA: false,
  },
  report: {
    defaultFormat: 'Excel',
    reportTitle: 'BÁO CÁO TÀI SẢN KẾT CẤU HẠ TẦNG THỦY LỢI',
    reportFooter: '© Bộ Nông nghiệp và Phát triển Nông thôn',
    showLogo: true,
  },
})
</script>

<template>
  <div>
    <h2 class="text-xl font-semibold mb-4">
      Cài Đặt Hệ Thống
    </h2>
    <div class="space-y-8">
      <!-- General Settings -->
      <div class="p-6 border rounded-lg">
        <h3 class="text-lg font-medium mb-4">
          Cài đặt chung
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-1.5">
            <label class="font-medium">Tên hệ thống</label>
            <Input v-model="settings.general.systemName" />
          </div>
          <div class="space-y-1.5">
            <label class="font-medium">Đơn vị quản lý</label>
            <Input v-model="settings.general.managingUnit" />
          </div>
          <div class="space-y-1.5">
            <label class="font-medium">Email liên hệ</label>
            <Input v-model="settings.general.contactEmail" type="email" />
          </div>
          <div class="space-y-1.5">
            <label class="font-medium">Số điện thoại hỗ trợ</label>
            <Input v-model="settings.general.supportPhone" />
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Map Settings -->
        <div class="p-6 border rounded-lg">
          <h3 class="text-lg font-medium mb-4">
            Cài đặt bản đồ
          </h3>
          <div class="space-y-4">
            <div class="space-y-1.5">
              <label class="font-medium">Bản đồ nền mặc định</label>
              <select v-model="settings.map.defaultBasemap" class="w-full border rounded px-2 py-1.5">
                <option>Google Maps</option>
                <option>Mapbox</option>
                <option>OpenStreetMap</option>
              </select>
            </div>
            <div class="flex gap-4">
              <div class="space-y-1.5 flex-1">
                <label class="font-medium">Vị trí mặc định (Kinh độ)</label>
                <Input v-model="settings.map.defaultLon" />
              </div>
              <div class="space-y-1.5 flex-1">
                <label class="font-medium">(Vĩ độ)</label>
                <Input v-model="settings.map.defaultLat" />
              </div>
            </div>
            <div class="space-y-1.5">
              <label class="font-medium">Mức zoom mặc định</label>
              <Input v-model="settings.map.defaultZoom" type="number" />
            </div>
            <div class="flex items-center gap-2">
              <input id="allow3d" v-model="settings.map.allow3D" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
              <label for="allow3d">Cho phép chế độ xem 3D</label>
            </div>
          </div>
        </div>

        <!-- Security Settings -->
        <div class="p-6 border rounded-lg">
          <h3 class="text-lg font-medium mb-4">
            Cài đặt bảo mật
          </h3>
          <div class="space-y-4">
            <div class="space-y-1.5">
              <label class="font-medium">Thời gian phiên đăng nhập (phút)</label>
              <Input v-model="settings.security.sessionTimeout" type="number" />
            </div>
            <div class="space-y-1.5">
              <label class="font-medium">Độ dài mật khẩu tối thiểu</label>
              <Input v-model="settings.security.minPasswordLength" type="number" />
            </div>
            <div class="space-y-1.5">
              <label class="font-medium">Số lần đăng nhập sai tối đa</label>
              <Input v-model="settings.security.maxLoginAttempts" type="number" />
            </div>
            <div class="flex items-center gap-2">
              <input id="require2fa" v-model="settings.security.require2FA" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
              <label for="require2fa">Yêu cầu xác thực hai yếu tố</label>
            </div>
          </div>
        </div>
      </div>

      <!-- Report Settings -->
      <div class="p-6 border rounded-lg">
        <h3 class="text-lg font-medium mb-4">
          Cài đặt báo cáo
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-1.5">
            <label class="font-medium">Định dạng xuất báo cáo mặc định</label>
            <select v-model="settings.report.defaultFormat" class="w-full border rounded px-2 py-1.5">
              <option>Excel</option>
              <option>PDF</option>
              <option>Word</option>
            </select>
          </div>
          <div class="space-y-1.5">
            <label class="font-medium">Tiêu đề báo cáo</label>
            <Input v-model="settings.report.reportTitle" />
          </div>
          <div class="space-y-1.5">
            <label class="font-medium">Chân trang báo cáo</label>
            <Input v-model="settings.report.reportFooter" />
          </div>
          <div class="flex items-center gap-2 pt-6">
            <input id="showlogo" v-model="settings.report.showLogo" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary">
            <label for="showlogo">Hiển thị logo trong báo cáo</label>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end gap-2 mt-8">
      <Button variant="outline">
        <Icon name="Undo2" class="h-4 w-4 mr-2" />
        Đặt lại
      </Button>
      <Button>
        <Icon name="Save" class="h-4 w-4 mr-2" />
        Lưu thay đổi
      </Button>
    </div>
  </div>
</template>
